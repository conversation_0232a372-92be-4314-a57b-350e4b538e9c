import { LitElement, css, html } from 'lit';
import { customElement, property, query } from 'lit/decorators.js';
import { SignalWatcher } from '@lit-labs/signals';

/**
 * Formatted text input component that preserves pasted formatting
 * Uses contenteditable div to allow rich text paste while integrating with forms
 */
@customElement('formatted-text-input')
export class FormattedTextInput extends SignalWatcher(LitElement) {
  static styles = css`
    :host {
      display: block;
    }

    .input-container {
      position: relative;
    }

    .editor {
      min-height: 100px;
      padding: 0.75rem;
      border: 1px solid var(--sl-input-border-color);
      border-radius: var(--sl-input-border-radius-medium);
      background: var(--sl-input-background-color);
      font-family: var(--sl-input-font-family);
      font-size: var(--sl-input-font-size-medium);
      font-weight: var(--sl-input-font-weight);
      line-height: var(--sl-input-line-height);
      color: var(--sl-input-color);
      outline: none;
      overflow-y: auto;
      max-height: 300px;
      transition: var(--sl-transition-fast) color, var(--sl-transition-fast) border, var(--sl-transition-fast) box-shadow;
    }

    .editor:focus {
      border-color: var(--sl-input-border-color-focus);
      box-shadow: 0 0 0 var(--sl-focus-ring-width) var(--sl-input-focus-ring-color);
    }

    .editor:empty::before {
      content: attr(data-placeholder);
      color: var(--sl-input-placeholder-color);
      pointer-events: none;
    }

    .editor p {
      margin: 0 0 1em 0;
    }

    .editor p:last-child {
      margin-bottom: 0;
    }

    .editor ul, .editor ol {
      margin: 0 0 1em 0;
      padding-left: 1.5em;
    }

    .editor li {
      margin-bottom: 0.25em;
    }

    .editor a {
      color: var(--sl-color-primary-600);
      text-decoration: underline;
    }

    .editor strong, .editor b {
      font-weight: bold;
    }

    .editor em, .editor i {
      font-style: italic;
    }

    .editor u {
      text-decoration: underline;
    }

    .editor h1, .editor h2, .editor h3, .editor h4, .editor h5, .editor h6 {
      margin: 0 0 0.5em 0;
      font-weight: bold;
    }

    .editor h1 { font-size: 1.5em; }
    .editor h2 { font-size: 1.3em; }
    .editor h3 { font-size: 1.1em; }

    .editor blockquote {
      margin: 0 0 1em 0;
      padding-left: 1em;
      border-left: 3px solid var(--sl-color-neutral-300);
      color: var(--sl-color-neutral-600);
    }

    .hidden {
      display: none;
    }

    .label {
      display: block;
      margin-bottom: 0.5rem;
      font-size: var(--sl-input-label-font-size-medium);
      font-weight: var(--sl-input-label-font-weight);
      color: var(--sl-input-label-color);
    }

    .hint {
      display: block;
      margin-top: 0.25rem;
      font-size: var(--sl-input-help-text-font-size-medium);
      color: var(--sl-input-help-text-color);
    }
  `;

  @property() name = '';
  @property() placeholder = 'Enter text...';
  @property() label = '';
  @property() hint = '';
  @property({ type: Boolean }) required = false;
  @property({ type: Boolean }) disabled = false;
  @property({ attribute: 'resize' }) resize = 'auto';

  private _value = '';

  @property()
  get value() {
    return this._value;
  }

  set value(newValue: string) {
    const oldValue = this._value;
    this._value = newValue;

    // Update the contenteditable div when value changes
    if (this.editor && this.editor.innerHTML !== newValue) {
      this.editor.innerHTML = newValue;
    }

    // Update the hidden input
    if (this.hiddenInput && this.hiddenInput.value !== newValue) {
      this.hiddenInput.value = newValue;
    }

    this.requestUpdate('value', oldValue);
  }

  @query('.editor') editor!: HTMLDivElement;
  @query('.hidden-input') hiddenInput!: HTMLInputElement;

  connectedCallback() {
    super.connectedCallback();
    // Set initial content if value is provided
    this.updateComplete.then(() => {
      if (this.value && this.editor) {
        this.editor.innerHTML = this.value;
      }
      // Set up form restoration compatibility
      this.setupFormCompatibility();
    });
  }

  private setupFormCompatibility() {
    if (!this.hiddenInput) return;

    // Override the hidden input's value setter to sync with our component
    const originalDescriptor = Object.getOwnPropertyDescriptor(HTMLInputElement.prototype, 'value');
    if (originalDescriptor) {
      Object.defineProperty(this.hiddenInput, 'value', {
        get: () => this._value,
        set: (newValue: string) => {
          // When the form restoration sets the value, update our component
          this.value = newValue;
        },
        configurable: true,
        enumerable: true
      });
    }
  }

  private handleInput() {
    const content = this.editor.innerHTML;
    this.value = content;
    
    // Update hidden input for form submission
    if (this.hiddenInput) {
      this.hiddenInput.value = content;
    }

    // Dispatch input event for form validation
    this.dispatchEvent(new CustomEvent('input', {
      detail: { value: content },
      bubbles: true,
      composed: true
    }));
  }

  private handlePaste(e: ClipboardEvent) {
    // Allow default paste behavior to preserve formatting
    // The contenteditable div will automatically handle rich text paste
    setTimeout(() => {
      this.handleInput();
    }, 0);
  }

  private handleKeyDown(e: KeyboardEvent) {
    // Handle Enter key to create proper paragraphs
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      document.execCommand('insertHTML', false, '<br><br>');
    }
  }

  render() {
    return html`
      <div class="input-container">
        ${this.label ? html`<label class="label">${this.label}${this.required ? '*' : ''}</label>` : ''}
        
        <div
          class="editor"
          contenteditable=${!this.disabled}
          data-placeholder=${this.placeholder}
          @input=${this.handleInput}
          @paste=${this.handlePaste}
          @keydown=${this.handleKeyDown}
        ></div>

        ${this.hint ? html`<div class="hint">${this.hint}</div>` : ''}

        <!-- Hidden input for form submission -->
        <input
          type="hidden"
          name=${this.name}
          class="hidden-input"
          .value=${this.value}
          ?required=${this.required}
        />
      </div>
    `;
  }
}

declare global {
  interface HTMLElementTagNameMap {
    'formatted-text-input': FormattedTextInput;
  }
}
