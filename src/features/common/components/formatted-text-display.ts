import { LitElement, css, html } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { unsafeHTML } from 'lit/directives/unsafe-html.js';
import { SignalWatcher } from '@lit-labs/signals';

/**
 * Component for safely displaying formatted text content
 * Handles both new HTML content and legacy plain text with backward compatibility
 */
@customElement('formatted-text-display')
export class FormattedTextDisplay extends SignalWatcher(LitElement) {
  static styles = css`
    :host {
      display: block;
    }

    .content {
      line-height: 1.6;
      word-wrap: break-word;
    }

    .content p {
      margin: 0 0 1em 0;
    }

    .content p:last-child {
      margin-bottom: 0;
    }

    .content ul, .content ol {
      margin: 0 0 1em 0;
      padding-left: 1.5em;
    }

    .content li {
      margin-bottom: 0.25em;
    }

    .content a {
      color: var(--sl-color-primary-600);
      text-decoration: underline;
    }

    .content a:hover {
      color: var(--sl-color-primary-700);
    }

    .content strong, .content b {
      font-weight: bold;
    }

    .content em, .content i {
      font-style: italic;
    }

    .content u {
      text-decoration: underline;
    }

    .content h1, .content h2, .content h3, .content h4, .content h5, .content h6 {
      margin: 0 0 0.5em 0;
      font-weight: bold;
    }

    .content h1 { font-size: 1.5em; }
    .content h2 { font-size: 1.3em; }
    .content h3 { font-size: 1.1em; }

    .content blockquote {
      margin: 0 0 1em 0;
      padding-left: 1em;
      border-left: 3px solid var(--sl-color-neutral-300);
      color: var(--sl-color-neutral-600);
    }

    .content br {
      line-height: 1.6;
    }

    /* Handle plain text that might have line breaks */
    .plain-text {
      white-space: pre-wrap;
    }
  `;

  @property() content = '';

  /**
   * Sanitizes HTML content to prevent XSS attacks
   * Removes dangerous elements and attributes while preserving formatting
   */
  private sanitizeHTML(html: string): string {
    // Create a temporary div to parse the HTML
    const temp = document.createElement('div');
    temp.innerHTML = html;

    // List of allowed tags for formatting
    const allowedTags = ['p', 'br', 'strong', 'b', 'em', 'i', 'u', 'a', 'ul', 'ol', 'li', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'blockquote'];
    
    // List of allowed attributes
    const allowedAttributes = ['href', 'target'];

    // Function to recursively clean nodes
    const cleanNode = (node: Node): Node | null => {
      if (node.nodeType === Node.TEXT_NODE) {
        return node;
      }

      if (node.nodeType === Node.ELEMENT_NODE) {
        const element = node as Element;
        const tagName = element.tagName.toLowerCase();

        // Remove disallowed tags
        if (!allowedTags.includes(tagName)) {
          // For disallowed tags, return their text content
          const textNode = document.createTextNode(element.textContent || '');
          return textNode;
        }

        // Create new clean element
        const cleanElement = document.createElement(tagName);

        // Copy allowed attributes
        for (const attr of element.attributes) {
          if (allowedAttributes.includes(attr.name.toLowerCase())) {
            // Special handling for href to prevent javascript: URLs
            if (attr.name.toLowerCase() === 'href') {
              const href = attr.value.toLowerCase();
              if (href.startsWith('http://') || href.startsWith('https://') || href.startsWith('mailto:')) {
                cleanElement.setAttribute(attr.name, attr.value);
              }
            } else {
              cleanElement.setAttribute(attr.name, attr.value);
            }
          }
        }

        // Recursively clean child nodes
        for (const child of Array.from(element.childNodes)) {
          const cleanChild = cleanNode(child);
          if (cleanChild) {
            cleanElement.appendChild(cleanChild);
          }
        }

        return cleanElement;
      }

      return null;
    };

    // Clean all child nodes
    const cleanDiv = document.createElement('div');
    for (const child of Array.from(temp.childNodes)) {
      const cleanChild = cleanNode(child);
      if (cleanChild) {
        cleanDiv.appendChild(cleanChild);
      }
    }

    return cleanDiv.innerHTML;
  }

  /**
   * Determines if content is HTML or plain text
   */
  private isHTML(content: string): boolean {
    // Simple check for HTML tags
    return /<[^>]+>/.test(content);
  }

  /**
   * Processes content for display
   */
  private processContent(): string {
    if (!this.content) return '';

    // If content contains HTML tags, sanitize and return as HTML
    if (this.isHTML(this.content)) {
      return this.sanitizeHTML(this.content);
    }

    // For plain text, convert line breaks to <br> tags and wrap in <p>
    const lines = this.content.split('\n').filter(line => line.trim() !== '');
    if (lines.length === 0) return '';
    
    if (lines.length === 1) {
      return `<p>${lines[0]}</p>`;
    }

    return lines.map(line => `<p>${line}</p>`).join('');
  }

  render() {
    const processedContent = this.processContent();
    
    if (!processedContent) {
      return html``;
    }

    return html`
      <div class="content">
        ${unsafeHTML(processedContent)}
      </div>
    `;
  }
}

declare global {
  interface HTMLElementTagNameMap {
    'formatted-text-display': FormattedTextDisplay;
  }
}
