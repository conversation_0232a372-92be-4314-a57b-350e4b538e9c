import { LitElement, css, html } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { unsafeHTML } from 'lit/directives/unsafe-html.js';
import { SignalWatcher } from '@lit-labs/signals';
import DOMPurify from 'dompurify';

/**
 * Component for safely displaying formatted text content
 * Handles both new HTML content and legacy plain text with backward compatibility
 */
@customElement('formatted-text-display')
export class FormattedTextDisplay extends SignalWatcher(LitElement) {
  static styles = css`
    :host {
      display: block;
    }

    .content {
      word-wrap: break-word;
    }

    /* Handle plain text that might have line breaks */
    .plain-text {
      white-space: pre-wrap;
    }
  `;

  @property() content = '';

  /**
   * Sanitizes HTML content using DOMPurify to prevent XSS attacks
   * Allows safe formatting elements while removing dangerous content
   */
  private sanitizeHTML(html: string): string {
    return DOMPurify.sanitize(html, {
      ALLOWED_TAGS: ['p', 'br', 'strong', 'b', 'em', 'i', 'u', 'a', 'ul', 'ol', 'li', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'blockquote'],
      ALLOWED_ATTR: ['href'],
      ALLOWED_URI_REGEXP: /^(?:(?:(?:f|ht)tps?|mailto):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i
    });
  }

  /**
   * Determines if content is HTML or plain text
   */
  private isHTML(content: string): boolean {
    // Simple check for HTML tags
    return /<[^>]+>/.test(content);
  }

  /**
   * Processes content for display
   */
  private processContent(): string {
    if (!this.content) return '';

    // If content contains HTML tags, sanitize and return as HTML
    if (this.isHTML(this.content)) {
      return this.sanitizeHTML(this.content);
    }

    // For plain text, convert line breaks to <br> tags and wrap in <p>
    const lines = this.content.split('\n').filter(line => line.trim() !== '');
    if (lines.length === 0) return '';
    
    if (lines.length === 1) {
      return `<p>${lines[0]}</p>`;
    }

    return lines.map(line => `<p>${line}</p>`).join('');
  }

  render() {
    const processedContent = this.processContent();
    
    if (!processedContent) {
      return html``;
    }

    return html`
      <div class="content">
        ${unsafeHTML(processedContent)}
      </div>
    `;
  }
}

declare global {
  interface HTMLElementTagNameMap {
    'formatted-text-display': FormattedTextDisplay;
  }
}
